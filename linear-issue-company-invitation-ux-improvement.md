# Linear Issue: Improve company invitation acceptance UX with direct redirect to companies page

**Priority**: High  
**Labels**: UX, Mobile, Company Management  
**Estimated Effort**: 2-3 hours  

## Problem

After accepting company invitations, users (especially mobile users) don't know where to find their new company due to poor flash message visibility. The current success message "<PERSON>zvání akceptováno" appears as a flash message that gets lost and provides no guidance on where to locate the newly connected company.

**Current Issues:**
- Flash messages have poor visibility and are easily missed
- Mobile users don't see desktop sidebar with company switcher
- No clear guidance on where to find the new company
- Success feedback is temporary and can disappear before user sees it

## Solution

Implement direct redirect to `/companies` page with success context and visual highlighting of the newly connected company.

## Technical Requirements

### 1. Modify CompanyConnections.vue
Update the `acceptConnection` method in `app/frontend/components/CompanyConnections.vue`:

```javascript
async acceptConnection(contract) {
  if (!confirm(`${this.$t('company_connections.confirm_accept', 'Opravdu chcete přijmout pozvání od')} ${contract.company.name}?`)) {
    return
  }

  try {
    await axios.post(`/api/v1/company_connections/${contract.id}/accept`)
    
    // Direct redirect with success context
    this.$router.push({
      path: '/companies',
      query: { 
        success: 'connected',
        company: contract.company.name,
        highlight: contract.company.id
      }
    });
    
  } catch (err) {
    console.error('Error accepting connection:', err)
    alert(this.$t('company_connections.error_accepting', 'Nelze zpracovat pozvání. Zkuste to prosím později.'))
  }
}
```

### 2. Enhance CompanyIndex.vue
Update `app/frontend/components/companies/CompanyIndex.vue` to handle success context:

#### 2.1 Add success message display
- Detect `success=connected` query parameter on component mount
- Show prominent success message at top of page (not flash message)
- Include company name from query parameter in success message

#### 2.2 Add company highlighting
- Use `highlight` query parameter to identify newly connected company
- Add visual highlighting (border, background color, or badge) to the highlighted company card
- Auto-scroll to highlighted company if it's not in viewport

#### 2.3 Clean up query parameters
- Clear success-related query parameters after displaying to prevent stale state on page refresh
- Use `this.$router.replace()` to update URL without success parameters

### 3. Styling Requirements
- Success message should be prominent and clearly visible
- Highlighted company card should have distinct visual treatment
- Mobile-responsive design for both success message and highlighting
- Success message should auto-dismiss after reasonable time (5-7 seconds)

## Acceptance Criteria

- [ ] User is immediately redirected to `/companies` page after successful invitation acceptance
- [ ] Success message appears prominently at top of companies page (not as flash message)
- [ ] Success message includes the name of the newly connected company
- [ ] Newly connected company is visually highlighted in the companies grid
- [ ] Page auto-scrolls to highlighted company if needed
- [ ] Success context is cleared from URL after display to prevent stale state
- [ ] Works seamlessly on both mobile and desktop devices
- [ ] Success message auto-dismisses after 5-7 seconds
- [ ] No flash messages are used for this success flow
- [ ] Existing company switching functionality remains unaffected

## Testing Scenarios

### Mobile Testing
- [ ] Accept invitation on mobile device
- [ ] Verify redirect works correctly
- [ ] Confirm success message is clearly visible on mobile
- [ ] Check that highlighted company is easily identifiable on mobile

### Desktop Testing  
- [ ] Accept invitation on desktop
- [ ] Verify redirect and highlighting work correctly
- [ ] Ensure success message doesn't interfere with desktop layout

### Edge Cases
- [ ] Test with multiple pending invitations
- [ ] Test when companies page is already open in another tab
- [ ] Test URL manipulation (direct access with success parameters)
- [ ] Test browser back/forward navigation after redirect

## Implementation Notes

- This replaces the current flash message approach which has poor visibility
- Maintains existing embedded/standalone modes for CompanyConnections component
- Uses existing router and query parameter patterns
- Leverages existing company grid layout in CompanyIndex.vue
- No changes needed to backend API endpoints

## Related Issues

- Addresses mobile UX issues mentioned in company switching discussions
- Improves user onboarding flow for multi-company users
- Complements existing company management interface

## Files to Modify

1. `app/frontend/components/CompanyConnections.vue` - Update acceptConnection method
2. `app/frontend/components/companies/CompanyIndex.vue` - Add success handling and highlighting
3. Potentially add new CSS classes for highlighting and success message styling
