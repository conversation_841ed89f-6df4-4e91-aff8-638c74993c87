// ABOUTME: Vuex store module for managing calendar state, events, works, and drag-drop operations
// ABOUTME: Handles fetching, updating, and optimistic UI updates for calendar items with rollback capability

import axios from '../utils/axiosSetup';
import { sendFlashMessage } from '../utils/flashMessage';
import dayjs from 'dayjs';

const state = {
  events: [],
  works: [],
  currentUserContractId: null, // Track current user's contract ID
  dragState: {
    isDragging: false,
    draggedItem: null,
    originalDate: null
  },
  loadingStates: {
    events: false,
    works: false,
    update: false
  },
  previousState: null, // For rollback functionality
  sidebarView: 'unscheduled', // 'unscheduled' or 'detail'
  selectedItem: null, // For detail view
  selectedItemType: null // 'work', 'event'
};

const getters = {
  // Get items organized by date
  itemsByDate: (state, getters, rootState, rootGetters) => (date) => {
    const dateString = date.toLocaleDateString('en-CA');

    const events = state.events.filter(event => {
      // Date filtering
      const eventStartDateString = new Date(event.start_time).toLocaleDateString('en-CA');
      const eventEndDateString = new Date(event.end_time).toLocaleDateString('en-CA');
      const isInDateRange = dateString >= eventStartDateString && dateString <= eventEndDateString;

      if (!isInDateRange) return false;

      // Status filtering for rejected events
      if (event.status === 'rejected') {
        // Only show rejected events to the original event owner (the person who created the event)
        const isEventOwner = event.contract && event.contract.id === state.currentUserContractId;
        return isEventOwner; // Only the event creator can see their rejected events
      }

      return true; // Show all other events (pending, approved, etc.)
    });

    const works = state.works.filter(work => {
      const workStartDateString = new Date(work.scheduled_start_date).toLocaleDateString('en-CA');
      const workEndDateString = work.scheduled_end_date ?
        new Date(work.scheduled_end_date).toLocaleDateString('en-CA') :
        workStartDateString;

      return dateString >= workStartDateString && dateString <= workEndDateString;
    });

    return { events, works };
  },
  
  // Check if user can edit an item with authorization logic
  canEditItem: (state, getters, rootState, rootGetters) => (item, itemType) => {
    // Check if user is authenticated
    if (!rootGetters['userStore/isAuthenticated']) {
      return false;
    }

    // Events can be dragged by users for their own events, or by managers for any event
    if (itemType === 'event') {
      // Check if this is the user's own event first
      const isOwnEvent = item.contract && item.contract.id === state.currentUserContractId;

      // Users can always drag their own events (regardless of manager status)
      // This allows regular employees to reschedule their own vacation/personal events
      if (isOwnEvent) {
        return true;
      }

      // For other users' events, only managers can drag them
      if (rootGetters['userStore/isManager']) {
        return true;
      }

      // Non-managers cannot drag other users' events
      return false;
    }
    
    // Works can be dragged based on authorization policy
    if (itemType === 'work') {
      return rootGetters['userStore/can']('can_manage_work_scheduling');
    }
    
    return false;
  },
  
  // Get all items for filtering/searching
  allItems: (state) => {
    return [
      ...state.events.map(e => ({ ...e, itemType: 'event' })),
      ...state.works.map(w => ({ ...w, itemType: 'work' }))
    ];
  },

  // Get current user contract ID
  currentUserContractId: (state) => state.currentUserContractId,

  // Get filtered events based on user permissions and event status
  filteredEvents: (state) => {
    return state.events.filter(event => {
      // Status filtering for rejected events
      if (event.status === 'rejected') {
        // Only show rejected events to the original event owner
        const isEventOwner = event.contract && event.contract.id === state.currentUserContractId;
        return isEventOwner; // Only the event creator can see their rejected events
      }

      return true; // Show all other events (pending, approved, etc.)
    });
  },

  // Get events for pending approval (excludes rejected events)
  pendingEvents: (state) => {
    return state.events.filter(event => {
      return event.status === 'pending';
    });
  },
  
  // Get unscheduled works (no confirmed dates)
  unscheduledWorks: (state) => {
    return state.works
      .filter(work => !work.scheduled_start_date)
      .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
  }
};

const mutations = {
  SET_EVENTS(state, events) {
    state.events = events;
  },
  
  SET_WORKS(state, works) {
    state.works = works;
  },
  
  ADD_EVENT(state, event) {
    state.events.push(event);
  },
  
  ADD_WORK(state, work) {
    state.works.push(work);
  },

  SET_CURRENT_USER_CONTRACT_ID(state, contractId) {
    state.currentUserContractId = contractId;
  },
  
  SET_LOADING_STATE(state, { type, loading }) {
    state.loadingStates[type] = loading;
  },
  
  SET_DRAG_STATE(state, dragState) {
    state.dragState = { ...state.dragState, ...dragState };
  },
  
  // Store state before making changes (for rollback)
  SAVE_PREVIOUS_STATE(state) {
    state.previousState = {
      events: [...state.events],
      works: [...state.works],
      currentUserContractId: state.currentUserContractId
    };
  },
  
  // Update a single event's date
  UPDATE_EVENT_DATE(state, { eventId, newStartTime, newEndTime }) {
    const event = state.events.find(e => e.id === eventId);
    if (event) {
      event.start_time = newStartTime;
      event.end_time = newEndTime;
    }
  },
  
  // Update a single work's date
  UPDATE_WORK_DATE(state, { workId, newStartDate, newEndDate }) {
    const work = state.works.find(w => w.id === workId);
    if (work) {
      work.scheduled_start_date = newStartDate;
      work.scheduled_end_date = newEndDate; // Can be null for single-day works
    }
  },

  
  // Revert to previous state (for failed API calls)
  REVERT_TO_PREVIOUS_STATE(state) {
    if (state.previousState) {
      state.events = state.previousState.events;
      state.works = state.previousState.works;
      state.currentUserContractId = state.previousState.currentUserContractId;
      state.previousState = null;
    }
  },
  
  CLEAR_PREVIOUS_STATE(state) {
    state.previousState = null;
  },
  
  // Sidebar management
  SET_SIDEBAR_VIEW(state, view) {
    state.sidebarView = view;
  },
  
  SET_SELECTED_ITEM(state, { item, itemType }) {
    state.selectedItem = item;
    state.selectedItemType = itemType;
    state.sidebarView = 'detail';
  },
  
  CLEAR_SELECTED_ITEM(state) {
    state.selectedItem = null;
    state.selectedItemType = null;
    state.sidebarView = 'unscheduled';
  },

  REMOVE_EVENT(state, eventId) {
    state.events = state.events.filter(event => event.id !== eventId);

    // Clear selected item if it's the deleted event
    if (state.selectedItem && state.selectedItemType === 'event' && state.selectedItem.id === eventId) {
      state.selectedItem = null;
      state.selectedItemType = null;
      state.sidebarView = 'unscheduled';
    }
  },
  
  UPDATE_EVENT_STATUS(state, { eventId, status }) {
    const event = state.events.find(e => e.id === eventId);
    if (event) {
      event.status = status;
    }
  },

  EVENT_CONFIRMED(state, eventId) {
    const event = state.events.find(e => e.id === eventId);
    if (event) {
      event.status = 'approved';
    }
  }
};

const actions = {
  // Fetch calendar data (events and works)
  async fetchCalendarData({ commit, rootState }, date = new Date()) {
    commit('SET_LOADING_STATE', { type: 'events', loading: true });
    commit('SET_LOADING_STATE', { type: 'works', loading: true });
    
    try {
      // Format date for API call (same as NewEventsIndexView)
      const formattedDate = date.toISOString().split('T')[0];
      
      // Fetch events and conditionally fetch works - use allSettled for partial success
      const promises = [
        axios.get('/api/v1/events/fetch', { params: { date: formattedDate } })
      ];
      
      // Only fetch works if enabled in company settings
      const showWorksMenu = rootState.companySettingsStore?.settings?.show_works_menu ?? true;
      if (showWorksMenu) {
        promises.push(axios.get('/api/v1/works'));
      }
      
      const results = await Promise.allSettled(promises);
      const [eventsResult, worksResult] = results;
      
      // Handle events from /api/v1/events/fetch response
      if (eventsResult.status === 'fulfilled') {
        const responseData = eventsResult.value.data;
        const eventsData = responseData.events || [];
        const currentUserContractId = responseData.current_user_contract_id;
        
        commit('SET_EVENTS', eventsData);
        commit('SET_CURRENT_USER_CONTRACT_ID', currentUserContractId);
      } else {
        console.error('🏪 ERROR: Failed to load events:', eventsResult.reason?.response?.data);
        sendFlashMessage('Failed to load events', 'error');
      }
      
      // Handle works (only if fetched)
      if (showWorksMenu) {
        if (worksResult && worksResult.status === 'fulfilled') {
          const worksData = worksResult.value.data.works || worksResult.value.data;
          commit('SET_WORKS', worksData);
        } else if (worksResult) {
          console.error('🏪 ERROR: Failed to load works:', worksResult.reason?.response?.data);
          sendFlashMessage('Failed to load works', 'error');
        }
      } else {
        // Clear works when feature is disabled
        commit('SET_WORKS', []);
      }
      
    } catch (error) {
      console.error('🏪 ERROR: Unexpected error fetching calendar data:', error);
      sendFlashMessage('Error loading calendar data', 'error');
    } finally {
      commit('SET_LOADING_STATE', { type: 'events', loading: false });
      commit('SET_LOADING_STATE', { type: 'works', loading: false });
    }
  },
  
  // Update event date with optimistic UI update
  async updateEventDate({ commit, state }, { eventId, newDate }) {
    // Save current state for rollback
    commit('SAVE_PREVIOUS_STATE');
    
    // Calculate new times maintaining duration
    const event = state.events.find(e => e.id === eventId);
    if (!event) return;
    
    const originalStart = new Date(event.start_time);
    const originalEnd = new Date(event.end_time);
    const duration = originalEnd - originalStart;
    
    const newStartTime = new Date(newDate);
    newStartTime.setHours(originalStart.getHours());
    newStartTime.setMinutes(originalStart.getMinutes());
    
    const newEndTime = new Date(newStartTime.getTime() + duration);
    
    // Optimistic update
    commit('UPDATE_EVENT_DATE', {
      eventId,
      newStartTime: newStartTime.toISOString(),
      newEndTime: newEndTime.toISOString()
    });
    
    commit('SET_LOADING_STATE', { type: 'update', loading: true });
    
    try {
      await axios.patch(`/api/v1/events/${eventId}/reschedule`, {
        start_time: newStartTime.toISOString(),
        end_time: newEndTime.toISOString()
      });
      
      commit('CLEAR_PREVIOUS_STATE');
      sendFlashMessage('Event rescheduled successfully', 'success');
    } catch (error) {
      console.error('🏪 ERROR: Failed to update event date:', error);
      commit('REVERT_TO_PREVIOUS_STATE');
      sendFlashMessage('Failed to reschedule event', 'error');
    } finally {
      commit('SET_LOADING_STATE', { type: 'update', loading: false });
    }
  },
  
  // Update work date with optimistic UI update (timezone-safe with dayjs)
  async updateWorkDate({ commit, state }, { workId, newDate }) {
    console.log('🏪 updateWorkDate called with:', { workId, newDate });
    
    // Save current state for rollback
    commit('SAVE_PREVIOUS_STATE');
    
    // Calculate new dates maintaining duration if multi-day
    const work = state.works.find(w => w.id === workId);
    if (!work) {
      console.error('🏪 ERROR: Work not found with ID:', workId);
      return;
    }
    
    console.log('📋 Found work:', { 
      id: work.id, 
      title: work.title, 
      currentStartDate: work.scheduled_start_date,
      currentEndDate: work.scheduled_end_date 
    });
    
    // Use dayjs for timezone-safe date handling (like MeetingForm.vue)
    const newStartDateString = dayjs(newDate).format('YYYY-MM-DD');
    
    let newEndDateString = null;
    if (work.scheduled_end_date) {
      // Calculate duration in days and maintain it
      const originalStart = dayjs(work.scheduled_start_date);
      const originalEnd = dayjs(work.scheduled_end_date);
      const durationDays = originalEnd.diff(originalStart, 'days');
      
      if (durationDays > 0) {
        newEndDateString = dayjs(newDate).add(durationDays, 'days').format('YYYY-MM-DD');
      }
    }
    
    console.log('📅 Calculated new dates:', { 
      newStartDateString, 
      newEndDateString 
    });
    
    // Optimistic update (store as date strings)
    commit('UPDATE_WORK_DATE', {
      workId,
      newStartDate: newStartDateString,
      newEndDate: newEndDateString
    });
    
    commit('SET_LOADING_STATE', { type: 'update', loading: true });
    
    try {
      console.log('🌐 Making API call to reschedule work...');
      // Send date-only strings to API (prevents timezone conversion bugs)
      const response = await axios.patch(`/api/v1/works/${workId}/reschedule`, {
        scheduled_start_date: newStartDateString,    // "2025-07-23" stays as "2025-07-23"
        scheduled_end_date: newEndDateString         // "2025-07-25" or null
      });
      
      console.log('✅ API call successful:', response.data);
      commit('CLEAR_PREVIOUS_STATE');
      sendFlashMessage('Zakázka byla úspěšně přesunuta', 'success');

    } catch (error) {

      console.error('🏪 ERROR: Failed to update work date:', error);
      console.error('🏪 ERROR Response:', error.response?.data);
      console.error('🏪 ERROR Status:', error.response?.status);
      commit('REVERT_TO_PREVIOUS_STATE');
      
      // Use backend error message directly - it's already perfect!
      const message = error.response?.data?.message || 
                     error.response?.data?.error || 
                     'Chyba při přesouvání práce';
      
      sendFlashMessage(message, 'error');
      throw error; // Re-throw to let calling component handle it
      
    } finally {
      commit('SET_LOADING_STATE', { type: 'update', loading: false });
    }
  },
  
  // Handle drag end for events and works
  async handleDragEnd({ dispatch, commit }, { item, itemType, newDate }) {
    commit('SET_DRAG_STATE', { isDragging: false, draggedItem: null });
    
    if (itemType === 'event') {
      await dispatch('updateEventDate', { eventId: item.id, newDate });
    } else if (itemType === 'work') {
      await dispatch('updateWorkDate', { workId: item.id, newDate });
    }
  },
  
  // Sidebar management actions
  showItemDetail({ commit }, { item, itemType }) {
    commit('SET_SELECTED_ITEM', { item, itemType });
  },
  
  closeSidebar({ commit }) {
    commit('CLEAR_SELECTED_ITEM');
  },
  
  // Drag-and-drop actions
  async approveEvent({ commit }, eventId) {
    try {
      const response = await axios.post(`/api/v1/events/${eventId}/confirm`);
      if (response.status === 200) {
        commit('UPDATE_EVENT_STATUS', { eventId, status: 'approved' });
        sendFlashMessage('Událost byla schválena.', 'success');
      }
    } catch (error) {
      sendFlashMessage('Schválení události se nezdařilo.', 'error');
      console.error('Error approving event:', error);
      throw error;
    }
  },

  async rejectEvent({ commit }, eventId) {
    try {
      const response = await axios.post(`/api/v1/events/${eventId}/reject`);
      if (response.status === 200) {
        commit('UPDATE_EVENT_STATUS', { eventId, status: 'rejected' });
        sendFlashMessage('Událost byla zamítnuta.', 'success');
      }
    } catch (error) {
      sendFlashMessage('Zamítnutí události se nezdařilo.', 'error');
      console.error('Error rejecting event:', error);
      throw error;
    }
  },

  async deleteEvent({ commit }, eventId) {
    try {
      const response = await axios.delete(`/api/v1/events/${eventId}`);
      if (response.status === 204 || response.status === 200) {
        commit('REMOVE_EVENT', eventId);
        sendFlashMessage('Událost byla úspěšně smazána.', 'success');
      }
    } catch (error) {
      sendFlashMessage('Smazání události se nezdařilo.', 'error');
      console.error('Error deleting event:', error);
      throw error;
    }
  },

  // Confirm/approve event - reuse the same endpoint as ownerStore
  async confirmEvent({ commit }, eventId) {
    try {
      const response = await axios.post(`/api/v1/events/${eventId}/confirm`);
      if (response.data.success) {
        commit('EVENT_CONFIRMED', eventId);
        sendFlashMessage('Událost byla schválena.', 'success');
        return response.data;
      }
    } catch (error) {
      sendFlashMessage('Schválení události se nezdařilo.', 'error');
      console.error('Error confirming event:', error);
      throw error;
    }
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};