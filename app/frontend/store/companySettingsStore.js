// ABOUTME: Vuex store module for managing company settings and feature visibility
// ABOUTME: Handles fetching and updating company-wide configuration settings

import axios from 'axios';
import { sendFlashMessage } from '../utils/flashMessage.js';

const state = {
  settings: {
    id: null,
    break_duration: 30,
    auto_end: true,
    auto_break: true,
    allow_overtime: true,
    timezone: 'Prague',
    approve_vacations: false,
    daily_team_reports: false,
    show_works_menu: true,
    show_bookings_menu: true,
    show_meetings_menu: true
  },
  loading: false,
  lastFetched: null
};

const getters = {
  // Feature visibility getters
  showWorksMenu: (state) => state.settings.show_works_menu,
  showBookingsMenu: (state) => state.settings.show_bookings_menu,
  showMeetingsMenu: (state) => state.settings.show_meetings_menu,
  
  // Settings getters
  allSettings: (state) => state.settings,
  isLoading: (state) => state.loading,
  
  // Check if settings have been fetched recently (within 5 minutes)
  areSettingsFresh: (state) => {
    if (!state.lastFetched) return false;
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    return state.lastFetched > fiveMinutesAgo;
  }
};

const mutations = {
  SET_SETTINGS(state, settings) {
    state.settings = { ...state.settings, ...settings };
    state.lastFetched = Date.now();
  },
  
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  
  UPDATE_SETTING(state, { key, value }) {
    state.settings[key] = value;
  },
  
  RESET_SETTINGS(state) {
    state.settings = {
      id: null,
      break_duration: 30,
      auto_end: true,
      auto_break: true,
      allow_overtime: true,
      timezone: 'Prague',
      approve_vacations: false,
      daily_team_reports: false,
      show_works_menu: true,
      show_bookings_menu: true,
      show_meetings_menu: true
    };
    state.lastFetched = null;
  }
};

const actions = {
  // Fetch company settings from API
  async fetchSettings({ commit, getters }, { force = false } = {}) {
    // Skip if settings are fresh unless forced
    if (!force && getters.areSettingsFresh) {
      return;
    }
    
    commit('SET_LOADING', true);
    
    try {
      const response = await axios.get('/api/v1/company_settings');
      const settings = response.data.company_setting;
      
      commit('SET_SETTINGS', settings);
      console.log('📋 Company settings fetched successfully:', settings);
      
    } catch (error) {
      console.error('📋 ERROR: Failed to fetch company settings:', error);
      sendFlashMessage('Failed to load company settings', 'error');
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  // Update company settings
  async updateSettings({ commit, state }, updates) {
    commit('SET_LOADING', true);
    
    try {
      const response = await axios.patch('/api/v1/company_settings', {
        company_setting: updates
      });
      
      const updatedSettings = response.data.company_setting;
      commit('SET_SETTINGS', updatedSettings);
      
      sendFlashMessage(response.data.message || 'Settings updated successfully', 'success');
      console.log('📋 Company settings updated successfully:', updatedSettings);
      
      return { success: true, settings: updatedSettings };
      
    } catch (error) {
      console.error('📋 ERROR: Failed to update company settings:', error);
      const errorMessage = error.response?.data?.errors?.join(', ') || 'Failed to update settings';
      sendFlashMessage(errorMessage, 'error');
      
      return { success: false, error: errorMessage };
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  // Update a single setting
  async updateSetting({ dispatch }, { key, value }) {
    return await dispatch('updateSettings', { [key]: value });
  },
  
  // Initialize settings when switching companies
  async initializeForCompany({ dispatch }) {
    await dispatch('fetchSettings', { force: true });
  },
  
  // Clear settings when logging out
  clearSettings({ commit }) {
    commit('RESET_SETTINGS');
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};