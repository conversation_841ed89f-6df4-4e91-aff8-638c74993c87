// src/store/index.js
import { createStore } from 'vuex';
import logStore from './logStore';
import userStore from './userStore';
import ownerStore from './ownerStore';
import calendarStore from './calendarStore';
import contractsStore from './contractsStore';
import networkStore from './networkStore';
import companySettingsStore from './companySettingsStore';

const store = createStore({
  modules: {
    logStore,
    userStore,
    ownerStore,
    calendarStore,
    contractsStore,
    networkStore,
    companySettingsStore
  },
});

export default store; 
