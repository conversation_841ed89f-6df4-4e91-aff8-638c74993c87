<template>
  <div class="user-menu" :class="{ 'dropdown-open': isOpen }">
    <div 
      v-if="isOpen" 
      class="mobile-overlay"
      @click="closeDropdown"
    ></div>
    <!-- New Menu Button -->
    <button
      @click="toggleDropdown"
      class="menu-button"
      :class="{ 'active': isOpen }"
      aria-label="Toggle menu"
    >
      <Menu :size="24" />
    </button>
    <!-- End New Menu Button -->
    
    
    <transition name="dropdown-fade">
      <div v-if="isOpen" class="user-dropdown">
        <LocalizedLink :to="'/companies'" class="dropdown-link profile" :use-anchor="true">
          <div>
            <h3 v-if="company.name">{{ company.name }}</h3>
          </div>
          <div>
            {{ userEmail }}
            <span v-if="isManager">({{ firstRole }})</span>
          </div>
          <span v-if="hasPlus" class="plus-ribbon">
            <Crown class="badge-icon" :size="18" /> plus
          </span>
        </LocalizedLink>
        <LocalizedLink :to="'/'" class="dropdown-link" :use-anchor="true">
          <Clock class="btn-icon" :size="20 " />
          {{ $t('summary', 'Přehled') }}
        </LocalizedLink>
        <LocalizedLink v-if="showWorksMenu" :to="'/works'" class="dropdown-link" :use-anchor="true">
          <LandPlot class="btn-icon" :size="20 " />
          {{ $t('company_works', 'Zakázky') }}
        </LocalizedLink>
        <!-- <a :href="hasPlus ? '/bookings' : '/pricing'" class="dropdown-link"> -->
        <LocalizedLink v-if="showBookingsMenu" :to="'/bookings'" class="dropdown-link" :use-anchor="true">
          <CalendarCheck class="btn-icon" :size="20" />
          {{ $t('online_bookings', 'Online rezervace') }}
          <span v-if="!hasPlus" class="badge">plus</span>
        </LocalizedLink>
        <LocalizedLink v-if="showMeetingsMenu" :to="'/meetings'" class="dropdown-link" :use-anchor="true">
          <UsersRound class="btn-icon" :size="20 " />
          {{ $t('meeting_assistant', 'Plánovač schůzek') }}
          <span v-if="!hasPlus" class="badge">plus</span>
        </LocalizedLink>
        <LocalizedLink :to="'/daily_logs/report'" class="dropdown-link" :use-anchor="true">
          <FileText class="btn-icon" :size="20 " />
          {{ $t('monthly_attendance', 'Měsíční docházka') }}
        </LocalizedLink>
        <LocalizedLink :to="'/holidays'" class="dropdown-link" :use-anchor="true">
          <Calendar class="btn-icon" :size="20" />
          {{ $t('holidays', 'Svátky') }}
        </LocalizedLink>
        <LocalizedLink :to="'/company_connections'" class="dropdown-link" :use-anchor="true">
          <Mailbox class="btn-icon" :size="20" />
          {{ $t('invitations', 'Pozvání') }}
        </LocalizedLink>
        <LocalizedLink :to="'/user_settings/edit'" class="dropdown-link" :use-anchor="true">
          <Settings class="btn-icon" :size="20" />
          {{ $t('personal_settings', 'Osobní nastavení') }}
        </LocalizedLink>
        <!-- <LocalizedLink :to="'/company_settings/edit'" class="dropdown-link" :use-anchor="true">
          <Settings class="btn-icon" :size="20" />
          {{ $t('company_settings', 'Nastavení firmy') }}
        </LocalizedLink> -->
        <a href="#" @click.prevent="handleLogout" class="dropdown-link">
          <LogOut class="btn-icon" :size="20" />
          {{ $t('logout', 'Odhlásit se') }}
        </a>
      </div>
    </transition>
  </div>
</template>

<script>
import authorizationMixin from '../mixins/authorizationMixin';
// Removed direct axios import - now using AuthService via userStore
// import axios from 'axios';
import { Menu, Warehouse, Mailbox, CircleUserRound, Settings,
    LogOut, FileText, Clock, LandPlot, CalendarCheck, Lock,
    Crown, UsersRound, Calendar
} from 'lucide-vue-next';
import LocalizedLink from './LocalizedLink.vue';

export default {
  name: 'HeaderDropdown',
  mixins: [authorizationMixin],
  components: {
    Menu, Warehouse, Mailbox, CircleUserRound, Settings, LogOut,
    FileText, Clock, LandPlot, CalendarCheck, Lock, Crown,
    UsersRound, Calendar, LocalizedLink
  },
  props: {
    user: {
      type: Number,
      required: true
    },
    company: {
      type: Object,
      default: () => ({ name: null })
    },
    subscription: {
      type: Object,
      default: () => ({ current_plan: null, available_features: [] })
    }
  },
  computed: {
    hasPlus() {
      return ['plus', 'premium'].includes(this.subscription.current_plan);
    },
    hasPremium() {
      return this.subscription.current_plan === 'premium';
    },
  },
  data() {
    return {
      isOpen: false,
      userEmail: '',
      userInitials: '...'
    }
  },
  methods: {
    toggleDropdown() {
      this.isOpen = !this.isOpen;
    },
    closeDropdown() {
      this.isOpen = false;
    },
    async handleLogout() {
      try {
        // Use Vuex userStore logout action which already integrates with AuthService 
        await this.$store.dispatch('userStore/logout');
        
        // Close dropdown
        this.closeDropdown();
        
        // Get current locale from route or fallback to 'cs'
        const currentLocale = this.$route.params.locale || 'cs';
        
        // Redirect to login with locale parameter
        this.$router.push({ 
          name: 'login', 
          params: { locale: currentLocale } 
        });
      } catch (error) {
        console.error('Logout failed:', error);
        // Even on error, try to redirect to ensure user doesn't stay logged in
        const currentLocale = this.$route.params.locale || 'cs';
        window.location.href = `/${currentLocale}/users/sign_in`;
      }
    },
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.closeDropdown();
      }
    },
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
    // Improved maintainability and encapsulation for state management
    this.userEmail = this.$store.getters['userStore/userEmail'] || '';
    this.userInitials = this.userEmail.slice(0, 2).toUpperCase();
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  }
}
</script>

<style scoped>

/* Dropdown Animation */
.dropdown-fade-enter-active,
.dropdown-fade-leave-active {
  transition: opacity 0.1s ease, transform 0.1s ease;
}

.dropdown-fade-enter-from,
.dropdown-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px); 
}

.dropdown-fade-enter-to,
.dropdown-fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

.user-menu {
  position: relative;
}

/* Style for the new menu button */
.menu-button {
  background: none;
  border: none;
  padding: 8px; 
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333; 
}

.menu-button:hover {
  color: #007BFF; 
}

.menu-button.active {
  color: #22C55E; 
}

.user-circle.active {
  background-color: #22C55E;
  color: white;
  transform: rotate(360deg);
}

.user-dropdown {
  position: absolute;
  right: 0;
  top: calc(100% + 8px);
  width: 350px;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border-radius: 4px;
  overflow: hidden;
  z-index: 800;
}

.dropdown-info {
  background-color: #22C55E;
  color: white;
}

.dropdown-link {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 1rem 2rem;
  color: #333;
  font-size: 14px;
  text-decoration: none;
  transition: background-color 0.2s ease;
  
  &.profile {
    padding: 2.5rem 2rem; 
    flex-direction: column;
    background-color: #f5f5f5;
  }
}

.dropdown-link:hover {
  background-color: #f5f5f5;
}

.badge {
    background-color: #007BFF;
    color: white;
    padding: 0.2em 0.5em;
    border-radius: 0.25em;
    font-size: 0.75em;
    margin-left: 0.5em;
  }

.badge-icon {
  margin-right: 0.25em;
}

.plus-ribbon {
  background-color: #007BFF;
  color: #fff;
  padding: 8px 16px;
  border-radius: 0 0 10px 10px;
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}


@media (max-width: 768px) {
  .user-dropdown {
    position: fixed;
    top: 45px;
    left: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    max-width: none;
    border-radius: 0;
    background: #fafafa;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-top: 1px solid #eee;
  }

  .dropdown-link {
    padding: 1.25rem 2rem;
    font-size:14px;
    border-bottom: 1px solid #eee;
    background: white;
  }

  .dropdown-link:last-child {
    border-bottom: none;
  }
}
</style>