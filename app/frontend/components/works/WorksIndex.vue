<template>
  <div class="works-index-container">
    <!-- Header -->
    <div class="works-header">
      <h1 class="text-2xl md:text-3xl font-bold text-gray-900">{{ $t('works.title', '<PERSON><PERSON> p<PERSON>') }}</h1>
      <div class="header-actions">
        <button @click="openServiceContractSummary()" class="btn btn-outline">
          <FileText :size="16" />
          {{ $t('service_contract_summary.nav_title', 'Firemní zakázky') }}
        </button>
        <!-- <button @click="openWorksSummary()" class="btn btn-outline">
          <Briefcase :size="16" />
          {{ $t('works_summary.nav_title', 'Přehled prací') }}
        </button> -->
        <button v-if="canManageWorks" @click="showServiceContractForm = true" class="btn btn-outline">
          {{ $t('works.new_service_contract', 'Nová zakázka') }}
        </button>
        <button v-if="canManageWorks" @click="showWorkForm = true" class="btn btn-primary">
          {{ $t('works.new_work', 'Nová práce') }}
        </button>
      </div>
    </div>

    <!-- Main Layout -->
    <div class="works-layout">
      <!-- Service Contracts Sidebar -->
      <div class="service-contracts-sidebar">
        <div class="sidebar-header">
          <div class="search-container">
            <input
              type="text"
              v-model="contractSearchQuery"
              @input="filterContracts"
              :placeholder="$t('works.search_contracts', 'Hledat práce a zakázky...')"
              class="search-input"
            >
          </div>
        </div>

        <div v-if="contractsLoading" class="sidebar-loading">
          <Clock :size="20" class="animate-spin text-gray-500" />
          <span class="text-sm text-gray-500">{{ $t('loading', 'Načítání...') }}</span>
        </div>

        <div v-else-if="filteredServiceContracts.length === 0" class="sidebar-empty">
          <div class="empty-contracts-content">
            <Briefcase :size="32" class="text-gray-300 mb-2" />
            <p class="text-sm text-gray-500 mb-3">{{ $t('works.no_contracts', 'Žádné zakázky') }}</p>
            <button v-if="canManageWorks" @click="showServiceContractForm = true" class="btn btn-primary btn-small">
              {{ $t('works.create_first_contract', 'Vytvořit první zakázku') }}
            </button>
          </div>
        </div>

        <div v-else class="contracts-list">
          <div
            v-for="contract in filteredServiceContracts"
            :key="contract.id"
            @click="selectContract(contract)"
            :class="['contract-item', { 'active': selectedContract?.id === contract.id }]"
          >
            <div class="contract-header">
              <h3 class="contract-title">{{ contract.title }}</h3>
              <span :class="['status-badge', getStatusClass(contract.status)]">
                {{ getStatusText(contract.status) }}
              </span>
            </div>
            <div class="contract-meta">
              <div class="contract-date">
                <Calendar :size="12" class="text-gray-400" />
                <span>{{ formatDate(contract.created_at) }}</span>
              </div>
              <div class="contract-time">
                <Clock :size="12" class="text-gray-400" />
                <span>{{ formatTime(contract.total_time_spent || 0) }}</span>
              </div>
            </div>
            <div v-if="contract.works && contract.works.length > 0" class="contract-works-count">
              {{ contract.works.length }} {{ contract.works.length === 1 ? 'práce' : 'prací' }}
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content Area -->
      <div class="main-content-area">
        <div v-if="!selectedContract" class="empty-state">
          <div class="empty-state-content">
            <Briefcase :size="48" class="text-gray-300" />
            <h3 class="text-lg font-medium text-gray-600">{{ $t('works.select_contract', 'Vyberte zakázku') }}</h3>
            <p class="text-gray-500">{{ $t('works.select_contract_desc', 'Vyberte zakázku ze seznamu pro zobrazení detailů a prací') }}</p>
          </div>
        </div>

        <div v-else class="contract-details">
          <!-- Contract Header -->
          <div class="contract-details-header">
            <div class="contract-info">
              <h2 class="contract-details-title">{{ selectedContract.title }}</h2>
              <div class="contract-details-meta">
                <span :class="['status-badge', getStatusClass(selectedContract.status)]">
                  {{ getStatusText(selectedContract.status) }}
                </span>
                <span class="text-sm text-gray-500">
                  {{ $t('works.contract_created', 'Vytvořeno') }}: {{ formatDate(selectedContract.created_at) }}
                </span>
              </div>
              <p v-if="selectedContract.description" class="contract-description">
                {{ selectedContract.description }}
              </p>
            </div>
            <div class="contract-actions">
              <button v-if="canManageWorks" @click="editContract(selectedContract)" class="btn btn-outline btn-small">
                <Edit2 :size="16" />
                {{ $t('edit', 'Upravit') }}
              </button>
            </div>
          </div>

          <!-- Contract Stats -->
          <div class="contract-stats">
            <div class="stat-item">
              <div class="stat-value">{{ selectedContract.works?.length || 0 }}</div>
              <div class="stat-label">{{ $t('works.total_works', 'Celkem prací') }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatTime(selectedContract.total_time_spent || 0) }}</div>
              <div class="stat-label">{{ $t('works.total_time', 'Celkový čas') }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ getActiveWorksCount(selectedContract.works) }}</div>
              <div class="stat-label">{{ $t('works.active_works', 'Aktivní práce') }}</div>
            </div>
          </div>

          <!-- Works Section -->
          <div class="works-section">
            <div class="works-section-header">
              <h3 class="text-lg font-semibold text-gray-800">{{ $t('works.works', 'Práce') }}</h3>
              <button v-if="canManageWorks" @click="createWorkForContract" class="btn btn-small">
                <Plus :size="16" />
                {{ $t('works.add_work', 'Přidat práci') }}
              </button>
            </div>

            <div v-if="worksLoading" class="works-loading">
              <Clock :size="24" class="animate-spin text-gray-500" />
            </div>

            <div v-else-if="!selectedContract.works || selectedContract.works.length === 0" class="works-empty">
              <div class="empty-works-content">
                <Users :size="32" class="text-gray-300" />
                <p class="text-gray-500">{{ $t('works.no_works_in_contract', 'Žádné práce v této zakázce') }}</p>
                <button v-if="canManageWorks" @click="createWorkForContract" class="btn btn-primary btn-small mt-2">
                  {{ $t('works.create_first_work', 'Vytvořit první práci') }}
                </button>
              </div>
            </div>

            <div v-else class="works-grid">
              <div v-for="work in selectedContract.works" :key="work.id" class="work-card-wrapper">
                <work-show
                  :work="work"
                  @deleted="workDeleted"
                  @updated="workUpdated"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <!-- Service Contract Form Modal -->
    <div v-if="showServiceContractForm" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>{{ editingContract ? $t('works.edit_service_contract', 'Upravit zakázku') : $t('works.new_service_contract', 'Nová zakázka') }}</h3>
          <button @click="closeServiceContractForm" class="close-btn">×</button>
        </div>
        <div class="central-modal-content">
          <service-contract-form
            :contract="editingContract"
            @cancel="closeServiceContractForm"
            @saved="serviceContractSaved"
          />
        </div>
      </div>
    </div>

    <!-- Work Form Modal -->
    <div v-if="showWorkForm" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>{{ $t('works.new_work', 'Nová práce') }}</h3>
          <button @click="showWorkForm = false" class="close-btn">×</button>
        </div>
        <div class="central-modal-content">
          <work-form
            :initial-service-contract-id="selectedContract?.id"
            @cancel="showWorkForm = false"
            @saved="workSaved"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import WorkForm from './WorkForm.vue';
import WorkShow from './WorkShow.vue';
import ServiceContractForm from './ServiceContractForm.vue';
import authorizationMixin from '../../mixins/authorizationMixin.js';
import { Clock, Calendar, Briefcase, Edit2, Plus, Users, FileText } from 'lucide-vue-next';

export default {
  mixins: [authorizationMixin],
  components: {
    WorkForm,
    WorkShow,
    ServiceContractForm,
    Clock,
    Calendar,
    Briefcase,
    Edit2,
    Plus,
    Users,
    FileText
  },
  data() {
    return {
      // Service Contracts
      serviceContracts: [],
      filteredServiceContracts: [],
      selectedContract: null,
      contractsLoading: true,
      contractSearchQuery: '',

      // Works
      works: [],
      worksLoading: false,

      // Modals
      showServiceContractForm: false,
      showWorkForm: false,
      editingContract: null,

      // Legacy (keeping for compatibility)
      loading: true,
      error: null,
      showForm: false,
      searchQuery: '',
      statusFilter: '',
      assignmentFilter: ''
    };
  },
  mounted() {
    this.fetchServiceContracts();
  },
  computed: {
    // Legacy computed properties (keeping for compatibility)
    filteredWorks() {
      return this.works;
    },
    hasActiveFilters() {
      return this.searchQuery || this.statusFilter || this.assignmentFilter;
    },
    canManageWorks() {
      return this.can('can_manage_works');
    }
  },
  methods: {
    // Service Contracts Management
    async fetchServiceContracts() {
      this.contractsLoading = true;
      try {
        const response = await axios.get('/api/v1/service_contracts');
        this.serviceContracts = response.data || [];
        this.filteredServiceContracts = [...this.serviceContracts];

        // Auto-select first contract if available
        if (this.serviceContracts.length > 0 && !this.selectedContract) {
          this.selectContract(this.serviceContracts[0]);
        }
      } catch (error) {
        console.error('Service contracts fetch error:', error);
        this.serviceContracts = [];
        this.filteredServiceContracts = [];

        // Only show error message if it's not a 404 (no contracts exist)
        if (error.response?.status !== 404) {
          this.error = error.response?.data?.error || this.$t('works.fetch_contracts_error', 'Chyba při načítání zakázek');
          this.showFlashMessage(this.error, 'error');
        }
      } finally {
        this.contractsLoading = false;
        this.loading = false;
      }
    },

    selectContract(contract) {
      this.selectedContract = contract;
      this.fetchWorksForContract(contract.id);
    },

    async fetchWorksForContract(contractId) {
      this.worksLoading = true;
      try {
        const response = await axios.get(`/api/v1/service_contracts/${contractId}`);
        if (this.selectedContract && this.selectedContract.id === contractId) {
          this.selectedContract = response.data;
        }
      } catch (error) {
        this.showFlashMessage(this.$t('works.fetch_works_error', 'Chyba při načítání prací'), 'error');
      } finally {
        this.worksLoading = false;
      }
    },

    filterContracts() {
      if (!this.contractSearchQuery) {
        this.filteredServiceContracts = [...this.serviceContracts];
        return;
      }

      const query = this.contractSearchQuery.toLowerCase();
      this.filteredServiceContracts = this.serviceContracts.filter(contract =>
        contract.title?.toLowerCase().includes(query) ||
        contract.description?.toLowerCase().includes(query)
      );
    },

    // Contract Actions
    editContract(contract) {
      this.editingContract = contract;
      this.showServiceContractForm = true;
    },

    createWorkForContract() {
      this.showWorkForm = true;
    },

    // Modal Management
    closeServiceContractForm() {
      this.showServiceContractForm = false;
      this.editingContract = null;
    },

    serviceContractSaved(contract) {
      if (this.editingContract) {
        // Update existing contract
        const index = this.serviceContracts.findIndex(c => c.id === contract.id);
        if (index !== -1) {
          this.serviceContracts.splice(index, 1, contract);
          this.selectedContract = contract;
        }
        this.showFlashMessage(this.$t('works.contract_updated', 'Zakázka byla aktualizována'), 'success');
      } else {
        // Add new contract
        this.serviceContracts.unshift(contract);
        this.selectContract(contract);
        this.showFlashMessage(this.$t('works.contract_created', 'Zakázka byla vytvořena'), 'success');
      }

      this.filterContracts();
      this.closeServiceContractForm();
    },

    // Work Management
    workSaved(work) {
      if (this.selectedContract) {
        // Add work to selected contract
        if (!this.selectedContract.works) {
          this.selectedContract.works = [];
        }
        this.selectedContract.works.unshift(work);

        // Update the contract in the main list
        const contractIndex = this.serviceContracts.findIndex(c => c.id === this.selectedContract.id);
        if (contractIndex !== -1) {
          this.serviceContracts[contractIndex] = { ...this.selectedContract };
        }
      }

      this.showWorkForm = false;
      this.showFlashMessage(this.$t('works.work_created', 'Práce byla vytvořena'), 'success');
    },

    workDeleted(workId) {
      if (this.selectedContract && this.selectedContract.works) {
        this.selectedContract.works = this.selectedContract.works.filter(w => w.id !== workId);

        // Update the contract in the main list
        const contractIndex = this.serviceContracts.findIndex(c => c.id === this.selectedContract.id);
        if (contractIndex !== -1) {
          this.serviceContracts[contractIndex] = { ...this.selectedContract };
        }
      }
      this.showFlashMessage(this.$t('works.work_deleted', 'Práce byla smazána'), 'success');
    },

    workUpdated(updatedWork) {
      if (this.selectedContract && this.selectedContract.works) {
        const index = this.selectedContract.works.findIndex(w => w.id === updatedWork.id);
        if (index !== -1) {
          this.selectedContract.works.splice(index, 1, updatedWork);

          // Update the contract in the main list
          const contractIndex = this.serviceContracts.findIndex(c => c.id === this.selectedContract.id);
          if (contractIndex !== -1) {
            this.serviceContracts[contractIndex] = { ...this.selectedContract };
          }
        }
      }
    },

    // Utility Methods
    getStatusClass(status) {
      const statusClasses = {
        'scheduled': 'status-scheduled',
        'in_progress': 'status-in-progress',
        'completed': 'status-completed',
        'cancelled': 'status-cancelled',
        'unprocessed': 'status-unprocessed'
      };
      return statusClasses[status] || 'status-default';
    },

    getStatusText(status) {
      const statusTexts = {
        'scheduled': this.$t('works.status.scheduled', 'Naplánována'),
        'in_progress': this.$t('works.status.in_progress', 'Probíhá'),
        'completed': this.$t('works.status.completed', 'Dokončena'),
        'cancelled': this.$t('works.status.cancelled', 'Zrušena'),
        'unprocessed': this.$t('works.status.unprocessed', 'Nezpracována')
      };
      return statusTexts[status] || status;
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('cs-CZ', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      });
    },

    formatTime(minutes) {
      if (!minutes || minutes === 0) return '0h';
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      if (hours === 0) return `${mins}m`;
      if (mins === 0) return `${hours}h`;
      return `${hours}h ${mins}m`;
    },

    getActiveWorksCount(works) {
      if (!works) return 0;
      return works.filter(work =>
        work.status === 'scheduled' || work.status === 'in_progress'
      ).length;
    },

    showFlashMessage(text, type) {
      const event = new CustomEvent('flashMessage', {
        detail: { text, type }
      });
      document.dispatchEvent(event);
    },

    // Legacy methods (keeping for compatibility)
    async fetchWorks() {
      // This is now handled by fetchServiceContracts
      return this.fetchServiceContracts();
    },

    applyFilters() {
      // Legacy method - filtering now handled differently
    },

    clearFilters() {
      this.searchQuery = '';
      this.statusFilter = '';
      this.assignmentFilter = '';
    },

    // Navigation methods for summary pages
    openServiceContractSummary() {
      const locale = this.$route.params.locale || 'cs';
      this.$router.push(`/${locale}/reports/service-contract-summary`);
    },

    openWorksSummary() {
      const locale = this.$route.params.locale || 'cs';
      this.$router.push(`/${locale}/reports/works-summary`);
    }
  }
};
</script>

<style scoped>
/* Main Container */
.works-index-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Header */
.works-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .works-header {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 0;
    padding-right: 0;
  }
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

/* Layout */
.works-layout {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
  min-height: 0;
}

@media (min-width: 1024px) {
  .works-layout {
    flex-direction: row;
  }
}

/* Service Contracts Sidebar */
.service-contracts-sidebar {
  width: 100%;
  flex-shrink: 0;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  height: fit-content;
  display: flex;
  flex-direction: column;
}

@media (min-width: 1024px) {
  .service-contracts-sidebar {
    width: 20rem;
    height: 100%;
    max-height: calc(100vh - 200px);
  }
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.search-container {
  margin-top: 0.75rem;
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
  border-color: transparent;
}

.sidebar-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: #6b7280;
}

.sidebar-empty {
  padding: 1rem;
  text-align: center;
}

.empty-contracts-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
}

.contracts-list {
  flex: 1;
  overflow-y: auto;
}

/* Contract Items */
.contract-item {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
}

.contract-item:hover {
  background-color: #f9fafb;
}

.contract-item.active {
  background-color: #eff6ff;
  border-left: 4px solid #3b82f6;
}

.contract-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.contract-title {
  font-weight: 500;
  color: #111827;
  font-size: 0.875rem;
  line-height: 1.25;
  flex: 1;
}

.contract-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.contract-date,
.contract-time {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.contract-works-count {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.25rem;
}

/* Status Badges */
.status-badge {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
  flex-shrink: 0;
}

.status-scheduled {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-in-progress {
  background-color: #fed7aa;
  color: #c2410c;
}

.status-completed {
  background-color: #dcfce7;
  color: #166534;
}

.status-cancelled {
  background-color: #fecaca;
  color: #dc2626;
}

.status-unprocessed {
  background-color: #f3f4f6;
  color: #374151;
}

.status-default {
  background-color: #f3f4f6;
  color: #4b5563;
}

/* Main Content Area */
.main-content-area {
  flex: 1;
  min-width: 0;
}

/* Empty State */
.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.empty-state-content {
  text-align: center;
  padding: 2rem;
}

.empty-state-content h3 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

/* Contract Details */
.contract-details {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.contract-details-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 640px) {
  .contract-details-header {
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
  }
}

.contract-info {
  flex: 1;
}

.contract-details-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
}

.contract-details-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.contract-description {
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.625;
}

.contract-actions {
  flex-shrink: 0;
}

/* Contract Stats */
.contract-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
}

.stat-label {
  font-size: 0.875rem;
  color: #4b5563;
  margin-top: 0.25rem;
}

/* Works Section */
.works-section {
  flex: 1;
  padding: 1.5rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.works-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.works-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.works-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-works-content {
  text-align: center;
  padding: 2rem;
}

.works-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  overflow-y: auto;
}

@media (min-width: 768px) {
  .works-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1280px) {
  .works-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.work-card-wrapper {
  height: fit-content;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-outline {
  border: 1px solid #d1d5db;
  color: #374151;
  background-color: white;
}

.btn-outline:hover {
  background-color: #f9fafb;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 1rem;
}

.modal-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 42rem;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.close-btn {
  color: #9ca3af;
  font-size: 1.5rem;
  font-weight: bold;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: none;
  border: none;
}

.close-btn:hover {
  color: #4b5563;
}

.central-modal-content {
  padding: 1rem;
  overflow-y: auto;
  flex: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .works-layout {
    flex-direction: column;
  }

  .service-contracts-sidebar {
    width: 100%;
    height: auto;
    max-height: 24rem;
  }

  .contract-stats {
    grid-template-columns: 1fr;
  }

  .works-grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 640px) {
  .contract-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 640px) {
  .works-header {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .header-actions {
    flex-direction: column;
  }

  .contract-details-header {
    padding: 1rem;
  }

  .contract-stats {
    padding: 1rem;
  }

  .works-section {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.25rem;
  }
}
</style>