<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          {{ $t('auth.accept_company_invitation', 'Připojit se ke společnosti') }}
        </h2>
        <p v-if="invitationData" class="mt-2 text-center text-sm text-gray-600">
          {{ $t('auth.company_invitation_for', 'By<PERSON> jste pozváni do společnosti {company}', { company: invitationData.company_name }) }}
        </p>
      </div>
      
      <div class="mt-8 space-y-6">
        <!-- Success Message -->
        <div v-if="successMessage" class="rounded-md bg-green-50 p-4">
          <div class="flex">
            <div class="ml-3">
              <p class="text-sm font-medium text-green-800">
                {{ successMessage }}
              </p>
            </div>
          </div>
        </div>
        
        <!-- Error Messages -->
        <div v-if="errorMessages.length > 0" class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                {{ $t('auth.invitation_failed', 'Přijetí pozvánky selhalo') }}
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="list-disc pl-5 space-y-1">
                  <li v-for="(error, index) in errorMessages" :key="index">
                    {{ error }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Invalid Token Warning -->
        <div v-if="!invitationToken || tokenInvalid" class="rounded-md bg-yellow-50 p-4">
          <div class="flex">
            <div class="ml-3">
              <p class="text-sm font-medium text-yellow-800">
                {{ $t('auth.invalid_invitation_link', 'Neplatný odkaz na pozvánku. Požádejte o novou pozvánku.') }}
              </p>
            </div>
          </div>
        </div>
        
        <!-- Invitation Details -->
        <div v-if="invitationData && !tokenInvalid" class="bg-blue-50 border border-blue-200 rounded-md p-4">
          <h3 class="text-sm font-medium text-blue-800">{{ $t('auth.invitation_details', 'Podrobnosti o pozvánce') }}</h3>
          <div class="mt-2 text-sm text-blue-700">
            <p><strong>{{ $t('auth.company', 'Společnost') }}:</strong> {{ invitationData.company_name }}</p>
            <p><strong>{{ $t('auth.invited_by', 'Pozval') }}:</strong> {{ invitationData.sender_name || invitationData.sender_email }}</p>
          </div>
        </div>
        
        <!-- Accept Button -->
        <div v-if="invitationToken && !tokenInvalid">
          <button
            @click="handleAcceptInvitation"
            :disabled="loading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ loading ? $t('auth.accepting_invitation', 'Přijímání pozvánky...') : $t('auth.accept_invitation_btn', 'Přijmout pozvánku') }}
          </button>
        </div>
        
        <!-- Login Notice for Non-Authenticated Users -->
        <div v-if="!isAuthenticated && invitationToken && !tokenInvalid" class="rounded-md bg-yellow-50 p-4">
          <div class="flex">
            <div class="ml-3">
              <p class="text-sm font-medium text-yellow-800">
                {{ $t('auth.login_required_for_invitation', 'Pro přijetí této pozvánky se musíte přihlásit.') }}
              </p>
              <div class="mt-2">
                <router-link 
                  :to="{ name: 'login', params: { locale: $route.params.locale }, query: { returnTo: $route.fullPath } }" 
                  class="text-sm font-medium text-yellow-800 underline hover:text-yellow-900"
                >
                  {{ $t('auth.login_now', 'Přihlásit se nyní') }}
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Back to Login Link -->
      <div class="text-center">
        <router-link 
          :to="{ name: 'login', params: { locale: $route.params.locale } }" 
          class="font-medium text-indigo-600 hover:text-indigo-500"
        >
          {{ $t('auth.back_to_login', 'Zpět na přihlášení') }}
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { mapGetters } from 'vuex'

export default {
  name: 'AcceptCompanyInvitationView',
  data() {
    return {
      invitationToken: null,
      invitationData: null,
      tokenInvalid: false,
      loading: false,
      successMessage: '',
      errorMessages: []
    }
  },
  computed: {
    ...mapGetters('user', ['isAuthenticated'])
  },
  mounted() {
    // Get the invitation token from URL params
    this.invitationToken = this.$route.query.token
    
    if (this.invitationToken) {
      this.validateToken()
    }
  },
  methods: {
    async validateToken() {
      try {
        const response = await axios.get(`/api/v1/invitations/${this.invitationToken}`, {
          headers: { 'Accept': 'application/json' }
        })
        
        if (response.data.valid) {
          const invitation = response.data.invitation
          this.invitationData = {
            company_name: invitation.company.name,
            sender_email: invitation.sender.email,
            sender_name: `${invitation.sender.first_name || ''} ${invitation.sender.last_name || ''}`.trim(),
            first_name: invitation.first_name,
            last_name: invitation.last_name,
            invitation_type: invitation.type,
            already_connected: invitation.already_connected
          }
          
          // If user is already connected, show appropriate message
          if (invitation.already_connected) {
            this.errorMessages = [this.$t('auth.already_connected_to_company', 'Již jste připojeni k této společnosti.')]
          }
          
          // If this is for a new user, redirect to new user invitation page
          if (invitation.type === 'new_user') {
            this.$router.push({
              name: 'acceptInvitation',
              params: { locale: this.$route.params.locale },
              query: { token: this.invitationToken }
            })
          }
        } else {
          this.tokenInvalid = true
          this.errorMessages = [response.data.error || this.$t('auth.invalid_invitation_token', 'Neplatný token pozvánky')]
        }
      } catch (error) {
        console.error('Token validation error:', error)
        this.tokenInvalid = true
        if (error.response && error.response.data) {
          this.errorMessages = [error.response.data.error || this.$t('auth.invalid_invitation_token', 'Invalid invitation token')]
        } else {
          this.errorMessages = [this.$t('auth.network_error', 'Chyba sítě. Prosím zkuste znovu.')]
        }
      }
    },
    
    async handleAcceptInvitation() {
      if (!this.isAuthenticated) {
        // Redirect to login with return URL
        this.$router.push({ 
          name: 'login', 
          params: { locale: this.$route.params.locale }, 
          query: { returnTo: this.$route.fullPath }
        })
        return
      }
      
      this.errorMessages = []
      this.successMessage = ''
      this.loading = true
      
      try {
        const response = await axios.post(`/api/v1/invitations/${this.invitationToken}/accept`, {
        }, {
          headers: { 'Accept': 'application/json' }
        })
        
        if (response.data.success) {
          // Store the JWT token if provided (for company switch)
          if (response.data.access_token) {
            localStorage.setItem('tymbox_jwt_token', response.data.access_token)
            this.$store.commit('user/SET_JWT_TOKEN', response.data.access_token)
            this.$store.commit('user/setAuthenticated', true)
            
            if (response.data.user) {
              this.$store.commit('user/SET_JWT_USER', response.data.user)
            }
          }
          
          this.successMessage = response.data.message || this.$t('auth.company_invitation_accepted', 'Pozvánka do společnosti byla úspěšně přijata!')
          
          // Redirect to dashboard after a short delay
          setTimeout(() => {
            this.$router.push({ name: 'dashboard', params: { locale: this.$route.params.locale } })
          }, 2000)
        } else {
          this.errorMessages = [response.data.error || this.$t('auth.invitation_failed', 'Přijetí pozvánky selhalo')]
        }
      } catch (error) {
        console.error('Company invitation acceptance error:', error)
        
        if (error.response && error.response.data) {
          const errorData = error.response.data
          if (errorData.error) {
            this.errorMessages = [errorData.error]
          } else if (errorData.details) {
            this.errorMessages = Object.values(errorData.details).flat()
          } else {
            this.errorMessages = [this.$t('auth.invitation_failed', 'Přijetí pozvánky selhalo')]
          }
        } else {
          this.errorMessages = [this.$t('auth.network_error', 'Chyba sítě. Prosím zkuste znovu.')]
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
