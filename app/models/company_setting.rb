class CompanySetting < ApplicationRecord
  belongs_to :company

  validates :break_duration, 
    presence: true,
    numericality: { 
      only_integer: true, 
      greater_than_or_equal_to: 0
    }

  after_initialize :set_defaults, if: :new_record?

  private

  def set_defaults
    self.break_duration ||= 30
    self.auto_end ||= true
    self.show_works_menu ||= true
    self.show_bookings_menu ||= true
    self.show_meetings_menu ||= true
  end
end