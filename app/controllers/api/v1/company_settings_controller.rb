# ABOUTME: API controller for company settings management with JWT authentication
# ABOUTME: Provides endpoints for fetching and updating company-wide configuration settings

class Api::V1::CompanySettingsController < Api::V1::ApiController
  
  include ActionPolicy::Controller

  # JWT authentication and company context handled by <PERSON><PERSON>Controller
  before_action :set_company_setting

  authorize :user, through: :current_user

  def show
    # Return current company settings
    authorize! @company_setting.company, to: :manage?
    
    render json: {
      company_setting: {
        id: @company_setting.id,
        break_duration: @company_setting.break_duration,
        auto_end: @company_setting.auto_end,
        auto_break: @company_setting.auto_break,
        allow_overtime: @company_setting.allow_overtime,
        timezone: @company_setting.timezone,
        approve_vacations: @company_setting.approve_vacations,
        daily_team_reports: @company_setting.daily_team_reports,
        show_works_menu: @company_setting.show_works_menu,
        show_bookings_menu: @company_setting.show_bookings_menu,
        show_meetings_menu: @company_setting.show_meetings_menu
      }
    }
  end

  def update
    # Update company settings
    authorize! @company_setting.company, to: :manage?

    if @company_setting.update(company_setting_params)
      render json: { 
        success: true, 
        company_setting: {
          id: @company_setting.id,
          break_duration: @company_setting.break_duration,
          auto_end: @company_setting.auto_end,
          auto_break: @company_setting.auto_break,
          allow_overtime: @company_setting.allow_overtime,
          timezone: @company_setting.timezone,
          approve_vacations: @company_setting.approve_vacations,
          daily_team_reports: @company_setting.daily_team_reports,
          show_works_menu: @company_setting.show_works_menu,
          show_bookings_menu: @company_setting.show_bookings_menu,
          show_meetings_menu: @company_setting.show_meetings_menu
        },
        message: I18n.t('controllers.company_settings.messages.updated', default: 'Company settings updated successfully') 
      }
    else
      render json: { 
        errors: @company_setting.errors.full_messages 
      }, status: :unprocessable_entity
    end
  end

  private

  def set_company_setting
    @company_setting = current_tenant.company_setting
  end

  def company_setting_params
    params.require(:company_setting).permit(
      :break_duration,
      :auto_end,
      :auto_break,
      :allow_overtime,
      :timezone,
      :approve_vacations,
      :daily_team_reports,
      :show_works_menu,
      :show_bookings_menu,
      :show_meetings_menu
    )
  end
end