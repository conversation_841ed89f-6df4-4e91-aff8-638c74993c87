module Api
  module V1
    class UsersController < ApiController
      before_action :authenticate_user!
      before_action :set_tenant_company
      
      # Example of usage in components
      # <div>
      # <div v-if="isOwner || isAdmin" class="admin-panel">Admin Controls</div>
      # <button v-if="can('can_manage_contracts')" @click="editContract">Edit Contract</button>
      # <div v-if="hasAnyRole(['owner', 'supervisor'])" class="supervisor-controls">
      #   Supervisor Controls
      # </div>
      # </div>

      def show
        user = current_user
        if @company
          # We rather get and make an array of roles,d espite yet it only takes one role per company
          # Ths might be much more useful in the future
          roles = user.company_user_roles.where(company: @company).joins(:role).pluck('roles.name')
          permissions = {
            can_manage_contracts: allowed_to?(:manage_contract?, @company),
            can_view_owner_section: allowed_to?(:view_owner_section?, @company),
            can_receive_team_status_emails?: allowed_to?(:receive_team_status_emails?, @company),
            can_manage_logo: allowed_to?(:manage_logo?, @company),
            can_manage_work_scheduling: allowed_to?(:manage_work_scheduling?, @company),
            can_view_all_company_events: allowed_to?(:view_all_company_events?, @company),
            can_manage_works: allowed_to?(:manage_works?, @company),
            can_view_works: allowed_to?(:view_works?, @company),
            # can_manage_employees: allowed_to?(:manage?, @company)
            # Add other commonly needed permissions here
            # can_manage_company: 
            # company_can_handle_bookings = can_manage_bookings ? 
            # add permissions according to the plus and premium plans specifically to get into the view ... 
          }
          
          current_plan = @company.current_plan&.name
          has_plus_plan = %w[premium plus].include?(current_plan)
          has_premium_plan = current_plan == "premium"

          render json: { 
            email: user.email, 
            role: roles.first,
            roles: roles,
            permissions: permissions,
            has_plus_plan: has_plus_plan,
            has_premium_plan: has_premium_plan,
            current_plan: current_plan,
            is_admin: user.admin_user?,
            company: {
              id: @company.id,
              name: @company.name,
              current_plan_name: current_plan
            }
          }
          
          puts "\n ---------- Roles: #{roles} ---------- \n\n"

        else
          render json: { email: user.email, role: nil, roles: [], has_plus_plan: false, has_premium_plan: false, is_admin: user.admin_user? }
        end
    
        #   if role
        #     render json: { role: role.name, email: user.email }
        #   else
        #     render json: { error: 'Authorization error' }, status: :not_found
        #   end
        # else
        #   render json: { role: nil, email: user.email }
        # end

      end

      private
      
      def set_tenant_company
        @company = ActsAsTenant.current_tenant
      end

    end
  end
end