module Api
  module V1
    class CompanyConnectionsController < ApiController
      # No tenant required for these actions since they operate across tenants
      
      def fetch
        ActsAsTenant.without_tenant do
          pending_contracts = Contract.where(email: current_user.email, user_id: nil)
          render json: pending_contracts.as_json(
            include: { company: { only: [:id, :name] } }
          )
        end
      end

      def accept
        ActsAsTenant.without_tenant do
          contract = Contract.find(params[:id])

          company_user_role = CompanyUserRole.find_or_initialize_by(
            user: current_user,
            company: contract.company,
            active: true
          )
          company_user_role.role = Role.find_by(name: 'employee')
          company_user_role.is_primary = true unless current_user.company_user_roles.exists?
          
          if company_user_role.save
            contract.update(user: current_user)
            render json: { success: true, message: I18n.t('invitations.messages.company_connection_accepted') }
          else
            render json: { error: I18n.t('invitations.errors.failed_to_accept_connection') }, status: :unprocessable_entity
          end
        end
      end
    end
  end
end
