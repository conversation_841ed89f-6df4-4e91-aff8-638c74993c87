Rails.application.routes.draw do

  # Add a JSON-specific route outside the locale scope
  get 'companies.json', to: 'companies#index', defaults: { format: :json }
  put 'companies/:id', to: 'companies#update'
  get 'holidays.json', to: 'holidays#index', defaults: { format: :json }


  scope "/:locale", locale: /#{I18n.available_locales.join("|")}/ do

    # Auth routes (Vue login/register) - must come before devise routes
    get 'users/sign_in', to: 'spa#index', as: :spa_login_override
    get 'users/sign_up', to: 'spa#index', as: :spa_register_override
    get 'forgot-password', to: 'spa#index', as: :spa_forgot_password
    get 'reset-password', to: 'spa#index', as: :spa_reset_password
    get 'confirm-email', to: 'spa#index', as: :spa_confirm_email
    get 'auth/accept-invitation', to: 'spa#index', as: :spa_accept_invitation
    get 'auth/accept-company-invitation', to: 'spa#index', as: :spa_accept_company_invitation

    # Server-rendered Admin routes (outside SPA)
    get 'admin', to: 'admin#index', as: :admin_root
    get 'admin/dashboard', to: 'admin#dashboard', as: :admin_dashboard
    get 'admin/companies', to: 'admin#companies', as: :admin_companies
    get 'admin/companies/:id', to: 'admin#show_company', as: :admin_company
    get 'admin/subscriptions', to: 'admin#subscriptions', as: :admin_subscriptions
    get 'admin/subscriptions/new', to: 'admin#new_subscription', as: :new_admin_subscription
    post 'admin/subscriptions', to: 'admin#create_subscription', as: :create_admin_subscription
    get 'admin/subscriptions/:id/edit', to: 'admin#edit_subscription', as: :edit_admin_subscription
    patch 'admin/subscriptions/:id', to: 'admin#update_subscription', as: :update_admin_subscription
    delete 'admin/subscriptions/:id', to: 'admin#destroy_subscription', as: :destroy_admin_subscription
    
    # Devise routes (authentication)
    devise_for :users, controllers: {
      registrations: 'users/registrations'
    }
    
    # Custom logout route
    # devise_scope :user do
    #   get '/logout', to: 'devise/sessions#destroy'
    # end


    # Public routes that need localization but don't require authentication
    
    # Terms routes
    get 'terms/:type', to: 'terms#show'
    
    # TODO: Test devise properly, especiall this
    # User signup
    get 'users/sign_up', to: 'users/registrations#new'
    
    # Routes that require authentication
    authenticated :user do
      # Specific page routes that need localization and authentication
      get 'companies', to: 'companies#index'
      get 'companies/new', to: 'companies#new'
      post 'companies', to: 'companies#create'
      post 'companies/switch', to: 'companies#switch'
      get 'companies/:id/edit', to: 'companies#edit'
      put 'companies/:id', to: 'companies#update'
      patch 'companies/:id', to: 'companies#update'
      get 'works', to: 'works#index'
      get 'bookings', to: 'bookings#index'
      get 'meetings', to: 'meetings#index'
      get 'daily_logs/report', to: 'daily_logs#report'
      get 'holidays', to: 'holidays#index'
      get 'company_connections', to: 'company_connections#index'
      get 'user_settings/edit', to: 'user_settings#edit'
      get 'contracts', to: 'contracts#index'
      get 'contracts/new', to: 'contracts#new'
      get 'events', to: 'events#index'
      
      # Root path for authenticated users
      root to: "mainbox#show", as: :authenticated_root_with_locale
    end
    
    devise_scope :user do
      # The root path for non-authenticated users should go to Vue SPA login
      root to: "spa#index", as: :unauthenticated_root_with_locale
    end

  end

  # Terms routes - accessible without locale prefix
  get 'terms/:type', to: 'terms#show'

  # Public booking routes
  get 'r/:company_slug/:slug', to: 'public_bookings#show', as: :public_booking
  post 'r/:company_slug/:slug/bookings', to: 'public_bookings#create'
  get 'r/:company_slug/:slug/calendar', to: 'public_bookings#calendar'
  
  # Booking management routes
  get 'r/:company_slug/manage/:token', to: 'public_bookings#manage', as: :manage_booking
  get 'r/:company_slug/bookings/:token', to: 'public_bookings#show_by_token', as: :show_booking_by_token
  patch 'r/:company_slug/bookings/:token', to: 'public_bookings#update_booking', as: :update_booking_by_token
  delete 'r/:company_slug/bookings/:token', to: 'public_bookings#cancel_booking', as: :cancel_booking_by_token
  
  # Private meetings routes
  get 'private_meetings/:token', to: 'public_meetings#show', as: :public_meeting
  patch 'private_meetings/:token', to: 'public_meetings#update'
  get 'private_meetings/:token/meeting', to: 'public_meetings#meeting'


# Need authentication, non locale scope

# Server-rendered Admin routes (non-localized, outside SPA)
get 'admin', to: 'admin#index'
get 'admin/dashboard', to: 'admin#dashboard'
get 'admin/companies', to: 'admin#companies'
get 'admin/companies/:id', to: 'admin#show_company'
get 'admin/subscriptions', to: 'admin#subscriptions'
get 'admin/subscriptions/new', to: 'admin#new_subscription'
post 'admin/subscriptions', to: 'admin#create_subscription'
get 'admin/subscriptions/:id/edit', to: 'admin#edit_subscription'
patch 'admin/subscriptions/:id', to: 'admin#update_subscription'
delete 'admin/subscriptions/:id', to: 'admin#destroy_subscription'

# All resource definitions with full CRUD operations
resources :companies, except: [:index, :new] do
  member do
    post :leave
    # TODO: Logo upload disabled due to security vulnerability - see Linear issue TYM-61
    # post :upload_logo
  end
  resource :settings, only: [:edit, :update], controller: 'company_settings' do
    # TODO: Logo upload disabled due to security vulnerability - see Linear issue TYM-61  
    # post :update_logo
  end
end

resources :contracts, except: [:index, :new]  do
  member do
    post :suspend
    post :reactivate
    post :terminate
    post :update_role
    post :resend_invitation
  end
  collection do
    get :fetch
    get :colleagues
  end
end

resources :company_connections, except: [:index] do
  collection do
    get :fetch
  end
  member do
    post :accept
  end
end

resources :daily_logs, except: [:report] do
  collection do
    post :create_for_report
    get :fetch
    get :last
    get :fetch_report_data
    get :fetch_day_data
    get :current_status
    get :team_summary
  end
end

resources :events, except: [:index] do
  collection do
    get :fetch
  end
end

resources :daily_activities do
  collection do
    post :start_work_activity
    get :current_work_activity
  end
  member do
    post :end_work_activity
  end
end

resources :assignments, only: [:index] do
  post :switch, on: :collection
end

resource :user_settings, only: [:update] do
  collection do
    get :index
  end
end

resources :breaks do
  collection do
    post :create_for_report
    get :today
  end
end

resources :works, except: [:index] do
  collection do
    get :fetch
    get :assigned
    get :today
  end
  member do
    post :take_assignment
    delete :leave_assignment
  end
end

resources :work_sessions, only: [] do
  member do
    post :check_in
    post :check_out
  end
end

resources :booking_links

resources :bookings, except: [:index] do
  collection do
    get :fetch
  end
  member do
    post :confirm
    post :cancel
    post :convert_to_work
  end
end


resource :user_profile, only: [:show, :edit, :update]

resources :holidays, except: [:index] do
  collection do
    post :sync
  end
end

# API routes
namespace :api do
  namespace :v1 do
    # Auth routes
    post 'auth/login', to: 'auth#login'
    post 'auth/logout', to: 'auth#logout'
    post 'auth/jwt_login', to: 'auth#jwt_login'
    post 'auth/jwt_logout', to: 'auth#jwt_logout'
    post 'auth/refresh_token', to: 'auth#refresh_token'
    post 'auth/restore_session', to: 'auth#restore_session'
    post 'auth/jwt_register', to: 'auth#jwt_register'
    post 'auth/request_password_reset', to: 'auth#request_password_reset'
    post 'auth/password_reset', to: 'auth#password_reset'
    put 'auth/change_password', to: 'auth#change_password'
    post 'auth/confirm_email', to: 'auth#confirm_email'
    post 'auth/resend_confirmation', to: 'auth#resend_confirmation'
    # JWT-based invitation system (refactored to InvitationsController)
    # Use param: :token to make route parameter match controller expectation (:token instead of :id)
    resources :invitations, only: [:create, :show], param: :token do
      member do
        post :accept
      end
    end
    
    # Legacy route compatibility (redirect to new endpoints)
    post 'auth/send_invitation', to: 'invitations#create'
    get 'auth/invitation_details', to: 'invitations#show'
    post 'auth/accept_invitation', to: 'invitations#accept'
    
    resource :user, only: [:show]
    resources :employees, only: [:index]
    
    resources :events, only: [:index, :create, :destroy] do
      collection do
        get :fetch
      end
      member do
        post :confirm
        post :reject
        patch :reschedule
      end
    end
    
    resources :daily_logs, only: [:index, :create, :update, :destroy] do
      collection do
        get :fetch
        get :last
        get :fetch_report_data
        get :fetch_employee_report_data
        get :current_status
        get :team_summary
        get :owner_work_summary
        get :service_contract_summary
        get :works_summary
        post :create_for_report
      end
    end
    
    resources :works, only: [:index, :create, :update, :destroy] do
      collection do
        get :assigned
        get :today
      end
      member do
        patch :reschedule
        post :force_close_activities
      end
    end

    resources :meetings, only: [:index, :show, :create, :update, :destroy] do
      collection do
        get :conflicts
      end
      member do
        post :resend_invitation
        post :extend_meeting_dates
        get :get_best_options
        post :add_user_to_confirmed_date
        post :confirm
      end
    end

    # Feedbacks endpoint (anonymous allowed)
    resources :feedbacks, only: [:create]

    resources :service_contracts, only: [:index, :show, :create, :update, :destroy]
    
    resources :daily_activities, only: [:create, :update] do
      collection do
        post :start_work_activity
        get :current_work_activity
      end
      member do
        patch :end_work_activity
      end
    end
    
    resources :contracts, only: [] do
      collection do
        get :colleagues
      end
    end
    
    resources :notifications, only: [:index, :destroy] do
      collection do
        get :unread_count
        get :for_mainbox
        put :mark_all_read
      end
      member do
        put :read
        put :process, action: :process_notification
      end
    end
    
    # Company connections for existing user invitations
    resources :company_connections, only: [] do
      collection do
        get :fetch
      end
      member do
        post :accept
      end
    end
    
    # JWT Company switching endpoint  
    resources :companies, only: [:index] do
      collection do
        post :switch_company
      end
    end
    
    # Company settings API endpoint
    resource :company_settings, only: [:show, :update]
    
    get 'subscription_status', to: 'subscription#status'
    get 'translations/:locale', to: 'translations#index'
    

  end
end

# Other utility routes
get 'reports/activities', to: 'reports#activities'
post 'reports/send_pdf', to: 'reports#send_pdf'
post 'newsletter/subscribe', to: 'newsletter#subscribe'
post 'cron/daily_team_status', to: 'cron#daily_team_status'
post 'cron/close_orphaned_activities', to: 'cron#close_orphaned_activities'

resources :subscriptions, only: [:create] do
  collection do
    post 'activate_trial'
    get 'request_subscription'
    post 'process_subscription_request'
  end
end

# Action Cable endpoint
mount ActionCable.server => '/cable'

# Fallback for root path if no locale is provided, redirect to default locale
root to: redirect("/#{I18n.default_locale}")

# Dynamic locale-aware SPA routes
scope "/:locale", locale: /#{I18n.available_locales.join("|")}/ do
  
  # Core routes
  get 'dashboard', to: 'spa#index', as: :spa_dashboard
  get 'mainbox', to: 'spa#index', as: :spa_mainbox
  
  # Bookings
  get 'bookings', to: 'spa#index', as: :spa_bookings
  get 'bookings/:id', to: 'spa#index', as: :spa_booking_show
  get 'booking_links', to: 'spa#index', as: :spa_booking_links
  get 'booking_links/:id', to: 'spa#index', as: :spa_booking_link_show
  
  # Meetings
  get 'meetings', to: 'spa#index', as: :spa_meetings
  get 'meetings/:id', to: 'spa#index', as: :spa_meeting_show
  
  # Daily Logs
  get 'daily_logs', to: 'spa#index', as: :spa_daily_logs
  get 'daily_logs/report', to: 'spa#index', as: :spa_daily_logs_report
  
  # Reports
  get 'reports/owner-monthly', to: 'spa#index', as: :spa_owner_monthly_reports
  get 'reports/owner-monthly/:id', to: 'spa#index', as: :spa_owner_monthly_report
  
  # Works
  get 'works', to: 'spa#index', as: :spa_works
  get 'works/:id', to: 'spa#index', as: :spa_work_show
  
  # Events
  get 'events', to: 'spa#index', as: :spa_events
  
  # Contracts
  get 'contracts', to: 'spa#index', as: :spa_contracts
  get 'contracts/:id', to: 'spa#index', as: :spa_contract_show
  
  # Companies
  get 'companies', to: 'spa#index', as: :spa_companies
  get 'companies/:id/edit', to: 'spa#index', as: :spa_company_edit
  get 'company_connections', to: 'spa#index', as: :spa_company_connections
  get 'companies/:id/settings/edit', to: 'spa#index', as: :spa_company_settings
  
  # Reports
  get 'reports/activities', to: 'spa#index', as: :spa_activities_report
  
  # User
  get 'user_profiles/:id/edit', to: 'spa#index', as: :spa_user_profile_edit
  get 'user_settings/edit', to: 'spa#index', as: :spa_user_settings
  
  # Holidays
  get 'holidays', to: 'spa#index', as: :spa_holidays
  
  # Assignments
  get 'assignments', to: 'spa#index', as: :spa_assignments
end

# Catch-all route for SPA - must be last!
# This handles any routes not explicitly defined above
get '*path', to: 'spa#index', constraints: lambda { |req|
  # Only catch HTML requests that:
  # 1. Are not AJAX/XHR requests
  # 2. Are HTML format
  # 3. Don't start with /api
  # 4. Don't start with /rails (ActiveStorage)
  # 5. Don't start with /assets
  # 6. Don't match public booking routes
  !req.xhr? && 
  req.format.html? && 
  !req.path.start_with?('/api') &&
  !req.path.start_with?('/rails') &&
  !req.path.start_with?('/assets') &&
  !req.path.start_with?('/vite') &&
  !req.path.start_with?('/cable') &&
  !req.path.match(/^\/r\//) &&
  !req.path.match(/^\/private_meetings\//)
}

end
